package com.facishare.paas.appframework.rest.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.privilege.rest.proxy.metadata.MetadataService;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.EmployeeService;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.coordination.CrmService;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverter;
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager;
import com.facishare.paas.appframework.metadata.dto.DataConflictsResult;
import com.facishare.paas.appframework.metadata.dto.DataSnapshotResult;
import com.facishare.paas.appframework.privilege.DataPrivilegeService;
import com.facishare.paas.appframework.rest.dto.data.*;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.condition.AbstractConditions;
import com.facishare.paas.metadata.api.condition.IConditions;
import com.facishare.paas.metadata.api.condition.RangeConditions;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.IRelatedListQuery;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.CountFieldDescribe;
import com.facishare.paas.metadata.impl.search.*;
import com.facishare.paas.metadata.support.JsonFieldHandler;
import com.facishare.paas.multiRegion.MultiRegionContextHolder;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ObjectDataRestServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private InfraServiceFacade infraServiceFacade;
    
    @Mock
    private FieldDataConverterManager fieldDataConverterManager;
    
    @Mock
    private DataPrivilegeService dataPrivilegeService;
    
    @Mock
    private CustomSceneService customSceneService;
    
    @Mock
    private CrmService crmService;
    
    @Mock
    private DataSnapshotLogicService dataSnapshotLogicService;
    
    @Mock
    private EmployeeService employeeService;
    
    @Mock
    private MetadataService metadataService;
    
    @InjectMocks
    private ObjectDataRestService objectDataRestService;
    
    private User testUser;
    private RequestContext testRequestContext;
    private IObjectDescribe testObjectDescribe;
    private IObjectData testObjectData;
    
    @BeforeEach
    void setUp() {
        // 创建标准测试用户
        testUser = User.systemUser("74255");
        
        // 创建标准测试请求上下文
        testRequestContext = RequestContext.builder()
                .tenantId("74255")
                .user(testUser)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        
        // 创建测试对象描述
        testObjectDescribe = mock(IObjectDescribe.class);
        when(testObjectDescribe.getId()).thenReturn("test-describe-id");
        when(testObjectDescribe.getApiName()).thenReturn("TestObject__c");
        when(testObjectDescribe.getPackage()).thenReturn("custom");
        
        // 创建测试对象数据
        testObjectData = new ObjectData();
        testObjectData.setId("test-data-id");
        testObjectData.setTenantId("74255");
        testObjectData.setDescribeApiName("TestObject__c");
        testObjectData.setDescribeId("test-describe-id");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建对象数据的正常场景，验证基本的创建流程
     */
    @Test
    @DisplayName("创建对象数据 - 正常场景")
    void testCreateObjectData_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            CreateData.Arg arg = new CreateData.Arg();
            arg.setDescAPIName("TestObject__c");
            arg.setJson("{\"name\":\"test\"}");
            arg.setCalculateDefaultValue(true);
            
            IObjectData createdData = new ObjectData();
            createdData.setId("created-id");
            createdData.setDescribeApiName("TestObject__c");
            
            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.saveObjectData(any(User.class), any(IObjectData.class), any())).thenReturn(createdData);
            doNothing().when(serviceFacade).log(any(User.class), any(EventType.class), any(ActionType.class), any(IObjectDescribe.class), anyList());
            
            // 执行被测试方法
            CreateData.Result result = objectDataRestService.createObjectData(arg, testRequestContext);
            
            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getObjectDataDocument());
            assertNotNull(result.getDescribeDocument());
            
            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).saveObjectData(any(User.class), any(IObjectData.class), any());
            verify(serviceFacade).log(any(User.class), eq(EventType.ADD), eq(ActionType.Add), eq(testObjectDescribe), anyList());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建对象数据时权限检查失败的异常场景
     */
    @Test
    @DisplayName("创建对象数据 - 权限检查失败抛出异常")
    void testCreateObjectDataThrowsValidateException_权限检查失败() {
        // 准备测试数据
        CreateData.Arg arg = new CreateData.Arg();
        arg.setDescAPIName("TestObject__c");
        arg.setJson("{\"name\":\"test\"}");
        
        RequestContext openApiContext = RequestContext.builder()
                .tenantId("74255")
                .user(testUser)
                .peerName("OpenAPI")
                .build();
        
        // 配置Mock行为
        when(serviceFacade.isAdmin(any(User.class))).thenReturn(false);
        
        // 执行并验证异常
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectDataRestService.createObjectData(arg, openApiContext);
        });
        
        // 验证异常信息
        assertTrue(exception.getMessage().contains("Bad Request"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量创建对象数据的正常场景
     */
    @Test
    @DisplayName("批量创建对象数据 - 正常场景")
    void testBatchCreateObjectData_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            BulkCreateData.Arg arg = new BulkCreateData.Arg();
            arg.setDescribeApiName("TestObject__c");
            arg.setDataListJson("[{\"name\":\"test1\"},{\"name\":\"test2\"}]");
            arg.setCalculateDefaultValue(true);
            
            List<IObjectData> createdDataList = Lists.newArrayList();
            IObjectData data1 = new ObjectData();
            data1.setId("id1");
            data1.setDescribeApiName("TestObject__c");
            createdDataList.add(data1);
            
            IObjectData data2 = new ObjectData();
            data2.setId("id2");
            data2.setDescribeApiName("TestObject__c");
            createdDataList.add(data2);
            
            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.bulkSaveObjectData(anyList(), any(User.class), any())).thenReturn(createdDataList);
            doNothing().when(serviceFacade).log(any(User.class), any(EventType.class), any(ActionType.class), any(IObjectDescribe.class), anyList());
            
            // 执行被测试方法
            BulkCreateData.Result result = objectDataRestService.batchCreateObjectData(arg, testRequestContext);
            
            // 验证结果
            assertNotNull(result);
            assertEquals("TestObject__c", result.getDescribeApiName());
            assertEquals(2, result.getData().size());
            
            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).bulkSaveObjectData(anyList(), any(User.class), any());
            verify(serviceFacade).log(any(User.class), eq(EventType.ADD), eq(ActionType.Add), eq(testObjectDescribe), anyList());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量创建对象数据时数据为空的场景
     */
    @Test
    @DisplayName("批量创建对象数据 - 数据为空返回空结果")
    void testBatchCreateObjectData_数据为空() {
        // 准备测试数据
        BulkCreateData.Arg arg = new BulkCreateData.Arg();
        arg.setDescribeApiName("TestObject__c");
        arg.setDataListJson("[]");
        
        // 执行被测试方法
        BulkCreateData.Result result = objectDataRestService.batchCreateObjectData(arg, testRequestContext);
        
        // 验证结果
        assertNotNull(result);
        assertNull(result.getData());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新对象数据的正常场景
     */
    @Test
    @DisplayName("更新对象数据 - 正常场景")
    void testUpdateObjectData_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            UpdateData.Arg arg = new UpdateData.Arg();
            arg.setDescribeAPIName("TestObject__c");
            arg.setDataId("test-data-id");
            arg.setDataJson("{\"name\":\"updated\"}");
            arg.setTriggerFlow(false);
            arg.setTool(false);

            IObjectData dbObjectData = new ObjectData();
            dbObjectData.setId("test-data-id");
            dbObjectData.setDescribeApiName("TestObject__c");

            IObjectData updatedData = new ObjectData();
            updatedData.setId("test-data-id");
            updatedData.setDescribeApiName("TestObject__c");

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.findObjectDataIgnoreAll(any(User.class), anyString(), anyString())).thenReturn(dbObjectData);
            when(serviceFacade.updateObjectData(any(User.class), any(IObjectData.class), any(IActionContext.class))).thenReturn(updatedData);

            // 执行被测试方法
            UpdateData.Result result = objectDataRestService.updateObjectData(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getObjectDataDocument());
            assertFalse(result.isVersionCheckBlocked());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).findObjectDataIgnoreAll(any(User.class), eq("test-data-id"), eq("TestObject__c"));
            verify(serviceFacade).updateObjectData(any(User.class), any(IObjectData.class), any(IActionContext.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新对象数据时数据版本冲突的场景
     */
    @Test
    @DisplayName("更新对象数据 - 数据版本冲突")
    void testUpdateObjectData_数据版本冲突() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            UpdateData.Arg arg = new UpdateData.Arg();
            arg.setDescribeAPIName("TestObject__c");
            arg.setDataId("test-data-id");
            arg.setDataJson("{\"name\":\"updated\",\"_version\":1}");
            arg.setProcessDataConflicts(true);

            IObjectData dbObjectData = new ObjectData();
            dbObjectData.setId("test-data-id");
            dbObjectData.setDescribeApiName("TestObject__c");
            dbObjectData.set("_version", 2); // 版本号不匹配

            DataConflictsResult conflictsResult = new DataConflictsResult();
            conflictsResult.setHasConflicts(true);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.findObjectDataIgnoreAll(any(User.class), anyString(), anyString())).thenReturn(dbObjectData);
            when(serviceFacade.processDataConflicts(any(User.class), any(IObjectDescribe.class), any(IObjectData.class), any(IObjectData.class)))
                    .thenReturn(conflictsResult);

            // 执行被测试方法
            UpdateData.Result result = objectDataRestService.updateObjectData(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isVersionCheckBlocked());
            assertNotNull(result.getDataConflictsResult());
            assertTrue(result.getDataConflictsResult().isHasConflicts());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).findObjectDataIgnoreAll(any(User.class), eq("test-data-id"), eq("TestObject__c"));
            verify(serviceFacade).processDataConflicts(any(User.class), eq(testObjectDescribe), eq(dbObjectData), any(IObjectData.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除对象数据的正常场景
     */
    @Test
    @DisplayName("删除对象数据 - 正常场景")
    void testInvalidByDataId_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            DeletedById.Arg arg = new DeletedById.Arg();
            arg.setDescribeAPIName("TestObject__c");
            arg.setDataId("test-data-id");

            IObjectData deletedData = new ObjectData();
            deletedData.setId("test-data-id");
            deletedData.setDescribeApiName("TestObject__c");

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.findObjectDataIgnoreFormula(any(User.class), anyString(), anyString())).thenReturn(testObjectData);
            when(serviceFacade.invalid(any(IObjectData.class), any(User.class), any(IActionContext.class))).thenReturn(deletedData);
            when(serviceFacade.findDetailDescribes(anyString(), anyString())).thenReturn(Lists.newArrayList());
            doNothing().when(serviceFacade).masterDetailLog(any(User.class), any(EventType.class), any(ActionType.class), anyMap(), anyList());

            // 执行被测试方法
            DeletedById.Result result = objectDataRestService.invalidByDataId(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getObjectDataDocument());
            assertNotNull(result.getDescribeDocument());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).findObjectDataIgnoreFormula(any(User.class), eq("test-data-id"), eq("TestObject__c"));
            verify(serviceFacade).invalid(any(IObjectData.class), any(User.class), any(IActionContext.class));
            verify(serviceFacade).masterDetailLog(any(User.class), eq(EventType.DELETE), eq(ActionType.Invalid), anyMap(), anyList());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找名称的正常场景
     */
    @Test
    @DisplayName("根据ID查找名称 - 正常场景")
    void testFindNameByIds_正常场景() {
        // 准备测试数据
        FindNameByIds.Arg arg = new FindNameByIds.Arg();
        arg.setDescribeAPIName("TestObject__c");
        arg.setIdList(Lists.newArrayList("id1", "id2"));
        arg.setNamingConvention("default");

        List<INameCache> nameCacheList = Lists.newArrayList();
        INameCache nameCache1 = mock(INameCache.class);
        when(nameCache1.getId()).thenReturn("id1");
        when(nameCache1.getName()).thenReturn("Name1");
        when(nameCache1.getDisplayName()).thenReturn("Display1");
        when(nameCache1.getLanguageName()).thenReturn("Lang1");
        nameCacheList.add(nameCache1);

        INameCache nameCache2 = mock(INameCache.class);
        when(nameCache2.getId()).thenReturn("id2");
        when(nameCache2.getName()).thenReturn("Name2");
        when(nameCache2.getDisplayName()).thenReturn("Display2");
        when(nameCache2.getLanguageName()).thenReturn("Lang2");
        nameCacheList.add(nameCache2);

        // 配置Mock行为
        when(serviceFacade.findRecordName(any(IActionContext.class), anyString(), anyList())).thenReturn(nameCacheList);

        // 执行被测试方法
        FindNameByIds.Result result = objectDataRestService.findNameByIds(arg, testRequestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getQueryResult());
        assertEquals(2, result.getQueryResult().getData().size());

        // 验证Mock交互
        verify(serviceFacade).findRecordName(any(IActionContext.class), eq("TestObject__c"), eq(Lists.newArrayList("id1", "id2")));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找名称时使用UI优化命名策略的场景
     */
    @Test
    @DisplayName("根据ID查找名称 - UI优化命名策略")
    void testFindNameByIds_UI优化命名策略() {
        // 准备测试数据
        FindNameByIds.Arg arg = new FindNameByIds.Arg();
        arg.setDescribeAPIName("TestObject__c");
        arg.setIdList(Lists.newArrayList("id1"));
        arg.setNamingConvention("ui_optimized");

        List<INameCache> nameCacheList = Lists.newArrayList();
        INameCache nameCache = mock(INameCache.class);
        when(nameCache.getId()).thenReturn("id1");
        when(nameCache.getName()).thenReturn("Name1");
        when(nameCache.getDisplayName()).thenReturn("Display1");
        when(nameCache.getLanguageName()).thenReturn("Lang1");
        nameCacheList.add(nameCache);

        IObjectDescribe objectDescribe = mock(IObjectDescribe.class);

        // 配置Mock行为
        when(serviceFacade.findRecordName(any(IActionContext.class), anyString(), anyList())).thenReturn(nameCacheList);
        when(serviceFacade.findObject(anyString(), anyString())).thenReturn(objectDescribe);

        // 执行被测试方法
        FindNameByIds.Result result = objectDataRestService.findNameByIds(arg, testRequestContext);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getQueryResult());
        assertEquals(1, result.getQueryResult().getData().size());

        Map<String, Object> nameMap = result.getQueryResult().getData().get(0);
        assertEquals("id1", nameMap.get("_id"));
        assertTrue(nameMap.containsKey("name"));

        // 验证Mock交互
        verify(serviceFacade).findRecordName(any(IActionContext.class), eq("TestObject__c"), eq(Lists.newArrayList("id1")));
        verify(serviceFacade).findObject(eq("74255"), eq("TestObject__c"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据搜索查询查找数据的正常场景
     */
    @Test
    @DisplayName("根据搜索查询查找数据 - 正常场景")
    void testFindBySearchQuery_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            FindByQuery.Arg arg = new FindByQuery.Arg();
            arg.setDescribeAPIName("TestObject__c");
            arg.setIncludeDescribe(true);

            ISearchQuery searchQuery = mock(ISearchQuery.class);
            when(searchQuery.getConditions()).thenReturn(Lists.newArrayList());
            when(searchQuery.getRangeConditions()).thenReturn(Lists.newArrayList());
            when(searchQuery.getOrders()).thenReturn(Lists.newArrayList());
            when(searchQuery.getLimit()).thenReturn(100);
            when(searchQuery.getOffset()).thenReturn(0);
            arg.setQuery(searchQuery);

            List<IObjectData> dataList = Lists.newArrayList(testObjectData);
            QueryResult<IObjectData> queryResult = new QueryResult<>();
            queryResult.setData(dataList);
            queryResult.setTotalNumber(1);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.findBySearchQuery(any(IActionContext.class), anyString(), any(SearchTemplateQuery.class)))
                    .thenReturn(queryResult);
            when(serviceFacade.getDataProjectionDataList(anyList(), anyList())).thenReturn(dataList);

            // 执行被测试方法
            FindByQuery.Result result = objectDataRestService.findBySearchQuery(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDescribeDocument());
            assertNotNull(result.getQueryResult());
            assertEquals(1, result.getQueryResult().getTotalNumber());
            assertEquals(1, result.getQueryResult().getData().size());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).findBySearchQuery(any(IActionContext.class), eq("TestObject__c"), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据搜索模板查询查找数据的正常场景
     */
    @Test
    @DisplayName("根据搜索模板查询查找数据 - 正常场景")
    void testFindBySearchTemplateQuery_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            FindBySearchTemplateQuery.Arg arg = new FindBySearchTemplateQuery.Arg();
            arg.setDescribeAPIName("TestObject__c");
            arg.setIncludeDescribe(true);
            arg.setSelectSpecialFields(false);

            SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
            searchTemplateQuery.setLimit(100);
            searchTemplateQuery.setOffset(0);
            arg.setSearchTemplateQuery(searchTemplateQuery);

            List<IObjectData> dataList = Lists.newArrayList(testObjectData);
            QueryResult<IObjectData> queryResult = new QueryResult<>();
            queryResult.setData(dataList);
            queryResult.setTotalNumber(1);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.findBySearchQuery(any(IActionContext.class), anyString(), any(SearchTemplateQuery.class)))
                    .thenReturn(queryResult);

            // 执行被测试方法
            FindBySearchTemplateQuery.Result result = objectDataRestService.findBySearchTemplateQuery(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDescribeDocument());
            assertNotNull(result.getQueryResult());
            assertEquals(1, result.getQueryResult().getTotalNumber());
            assertEquals(1, result.getQueryResult().getData().size());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).findBySearchQuery(any(IActionContext.class), eq("TestObject__c"), any(SearchTemplateQuery.class));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找数据的正常场景
     */
    @Test
    @DisplayName("根据ID查找数据 - 正常场景")
    void testFindDataById_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            FindById.Arg arg = new FindById.Arg();
            arg.setDescAPIName("TestObject__c");
            arg.setDataId("test-data-id");
            arg.setIncludeDescribe(true);
            arg.setIncludeDeleted(false);
            arg.setIncludeInvalid(false);
            arg.setCheckPrivilege(false);
            arg.setFillExtendField(false);
            arg.setIncludeLookup(false);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.findObjectData(any(IActionContext.class), anyString(), any(IObjectDescribe.class)))
                    .thenReturn(testObjectData);

            // 执行被测试方法
            FindById.Result result = objectDataRestService.findDataById(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDescribeDocument());
            assertNotNull(result.getObjectDataDocument());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).findObjectData(any(IActionContext.class), eq("test-data-id"), eq(testObjectDescribe));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID查找数据时包含已删除数据的场景
     */
    @Test
    @DisplayName("根据ID查找数据 - 包含已删除数据")
    void testFindDataById_包含已删除数据() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            FindById.Arg arg = new FindById.Arg();
            arg.setDescAPIName("TestObject__c");
            arg.setDataId("test-data-id");
            arg.setIncludeDescribe(false);
            arg.setIncludeDeleted(true);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.findObjectDataIgnoreStatus(any(User.class), anyString(), anyString()))
                    .thenReturn(testObjectData);

            // 执行被测试方法
            FindById.Result result = objectDataRestService.findDataById(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNull(result.getDescribeDocument());
            assertNotNull(result.getObjectDataDocument());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).findObjectDataIgnoreStatus(any(User.class), eq("test-data-id"), eq("TestObject__c"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据多个ID查找数据的正常场景
     */
    @Test
    @DisplayName("根据多个ID查找数据 - 正常场景")
    void testFindByIds_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            FindDataByIds.Arg arg = new FindDataByIds.Arg();
            arg.setDescribeAPIName("TestObject__c");
            arg.setIdList(Lists.newArrayList("id1", "id2"));
            arg.setFillInfo(false);
            arg.setFillExtendField(false);

            List<IObjectData> dataList = Lists.newArrayList(testObjectData);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObjectDataByIds(any(IActionContext.class), anyList(), anyString()))
                    .thenReturn(dataList);

            // 执行被测试方法
            FindDataByIds.Result result = objectDataRestService.findByIds(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getDataList());
            assertEquals(1, result.getDataList().size());

            // 验证Mock交互
            verify(serviceFacade).findObjectDataByIds(any(IActionContext.class), eq(Lists.newArrayList("id1", "id2")), eq("TestObject__c"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试恢复数据的正常场景
     */
    @Test
    @DisplayName("恢复数据 - 正常场景")
    void testRecover_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            RecoverData.Arg arg = new RecoverData.Arg();
            arg.setDescribeAPIName("TestObject__c");
            arg.setDataId("test-data-id");

            IObjectData recoveredData = new ObjectData();
            recoveredData.setId("test-data-id");
            recoveredData.setDescribeApiName("TestObject__c");

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.recover(any(IObjectData.class), any(IActionContext.class), any(User.class)))
                    .thenReturn(recoveredData);
            when(serviceFacade.findDetailDescribes(anyString(), anyString())).thenReturn(Lists.newArrayList());
            doNothing().when(serviceFacade).masterDetailLog(any(User.class), any(EventType.class), any(ActionType.class), anyMap(), anyList());

            // 执行被测试方法
            RecoverData.Result result = objectDataRestService.recover(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getObjectDataDocument());
            assertNotNull(result.getDescribeDocument());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).recover(any(IObjectData.class), any(IActionContext.class), any(User.class));
            verify(serviceFacade).masterDetailLog(any(User.class), eq(EventType.MODIFY), eq(ActionType.Recovery), anyMap(), anyList());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找相关列表的正常场景
     */
    @Test
    @DisplayName("查找相关列表 - 正常场景")
    void testFindRelatedList_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            FindRelatedList.Arg arg = new FindRelatedList.Arg();
            arg.setIncludeDescribe(true);

            IRelatedListQuery relatedListQuery = mock(IRelatedListQuery.class);
            when(relatedListQuery.getTenantId()).thenReturn("74255");
            arg.setQuery(relatedListQuery);

            IRelatedListQuery.QueryResult<IObjectData> queryResult = mock(IRelatedListQuery.QueryResult.class);
            List<IRelatedListQuery.QueryResult.RelatedObjectResult<IObjectData>> resultList = Lists.newArrayList();
            IRelatedListQuery.QueryResult.RelatedObjectResult<IObjectData> relatedResult =
                    mock(IRelatedListQuery.QueryResult.RelatedObjectResult.class);
            when(relatedResult.getChildApiName()).thenReturn("ChildObject__c");
            resultList.add(relatedResult);
            when(queryResult.getResultList()).thenReturn(resultList);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findRelatedObjectData(any(IRelatedListQuery.class), any(User.class)))
                    .thenReturn(queryResult);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);

            // 执行被测试方法
            FindRelatedList.Result result = objectDataRestService.findRelatedList(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);

            // 验证Mock交互
            verify(serviceFacade).findRelatedObjectData(any(IRelatedListQuery.class), any(User.class));
            verify(serviceFacade).findObject(eq("74255"), eq("ChildObject__c"));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除数据的正常场景
     */
    @Test
    @DisplayName("删除数据 - 正常场景")
    void testDelete_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class)) {
            // 准备测试数据
            DeletedById.Arg arg = new DeletedById.Arg();
            arg.setDescribeAPIName("TestObject__c");
            arg.setDataId("test-data-id");
            arg.setIncludeDescribe(true);

            List<IObjectData> deletedList = Lists.newArrayList(testObjectData);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.bulkDelete(anyList(), any(User.class))).thenReturn(deletedList);
            doNothing().when(serviceFacade).log(any(User.class), any(EventType.class), any(ActionType.class), any(IObjectDescribe.class), anyList());

            // 执行被测试方法
            DeletedById.Result result = objectDataRestService.delete(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertNotNull(result.getObjectDataDocument());
            assertNotNull(result.getDescribeDocument());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).bulkDelete(anyList(), any(User.class));
            verify(serviceFacade).log(any(User.class), eq(EventType.DELETE), eq(ActionType.Delete), eq(testObjectDescribe), eq(deletedList));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量更新数据的正常场景
     */
    @Test
    @DisplayName("批量更新数据 - 正常场景")
    void testBatchUpdate_正常场景() {
        try (MockedStatic<UdobjGrayConfig> mockedUdobjGrayConfig = mockStatic(UdobjGrayConfig.class);
             MockedStatic<RequestContextManager> mockedRequestContextManager = mockStatic(RequestContextManager.class)) {

            // 准备测试数据
            BatchUpdateData.Arg arg = new BatchUpdateData.Arg();
            arg.setDescribeApiName("TestObject__c");
            arg.setDataListJson("[{\"_id\":\"id1\",\"name\":\"updated1\"},{\"_id\":\"id2\",\"name\":\"updated2\"}]");

            List<IObjectData> dbObjectDataList = Lists.newArrayList();
            IObjectData dbData1 = new ObjectData();
            dbData1.setId("id1");
            dbData1.setDescribeApiName("TestObject__c");
            dbObjectDataList.add(dbData1);

            IObjectData dbData2 = new ObjectData();
            dbData2.setId("id2");
            dbData2.setDescribeApiName("TestObject__c");
            dbObjectDataList.add(dbData2);

            List<IObjectData> updatedDataList = Lists.newArrayList(dbData1, dbData2);

            // 配置Mock行为
            mockedUdobjGrayConfig.when(() -> UdobjGrayConfig.isAllow(any(), any())).thenReturn(false);
            mockedRequestContextManager.when(RequestContextManager::getContext).thenReturn(testRequestContext);
            when(serviceFacade.findObject(anyString(), anyString())).thenReturn(testObjectDescribe);
            when(serviceFacade.findObjectDataByIdsIgnoreAll(anyString(), anyList(), anyString()))
                    .thenReturn(dbObjectDataList);
            doNothing().when(serviceFacade).calculateForBatchEditData(any(User.class), anyList(), anyList(), any(IObjectDescribe.class));
            when(serviceFacade.batchUpdateByFields(any(IActionContext.class), anyList(), anyList()))
                    .thenReturn(updatedDataList);

            // 执行被测试方法
            BatchUpdateData.Result result = objectDataRestService.batchUpdate(arg, testRequestContext);

            // 验证结果
            assertNotNull(result);
            assertEquals("OK", result.getResult());

            // 验证Mock交互
            verify(serviceFacade).findObject("74255", "TestObject__c");
            verify(serviceFacade).findObjectDataByIdsIgnoreAll(eq("74255"), anyList(), eq("TestObject__c"));
            verify(serviceFacade).calculateForBatchEditData(any(User.class), anyList(), anyList(), eq(testObjectDescribe));
            verify(serviceFacade).batchUpdateByFields(any(IActionContext.class), anyList(), anyList());
        }
    }
}
